"""
系统设置控制器，用于管理系统范围的设置
"""

from typing import Dict, Any, Optional, List, Set, Tuple
from collections import defaultdict

from app.models.strm import SystemSettings, MediaServer, FileType
from app.core.crud import CRUDBase
from app.core.ctx import CTX_USER_ID
from app.core.exceptions import HTTPException
from app.log.log import set_sql_logging_enabled


class SystemSettingsController(CRUDBase):
    """系统设置控制器"""

    async def init_sql_logging(self):
        """初始化SQL日志设置"""
        try:
            settings = await SystemSettings.all().first()
            if settings and hasattr(settings, 'enable_sql_logging'):
                set_sql_logging_enabled(settings.enable_sql_logging)
            else:
                # 如果没有设置或字段不存在，默认禁用
                set_sql_logging_enabled(False)
        except Exception as e:
            # 如果出现任何错误，默认禁用SQL日志
            set_sql_logging_enabled(False)
            print(f"初始化SQL日志设置时出错: {e}")

    async def get_settings(self) -> Optional[Dict[str, Any]]:
        """
        获取系统设置

        如果不存在设置记录，返回None

        Returns:
            系统设置字典或None
        """
        # 获取第一条系统设置记录，通常只会有一条
        settings = await SystemSettings.all().first()
        if not settings:
            return None

        # 获取默认下载服务器信息
        default_download_server = None
        if settings.default_download_server_id:
            server = await MediaServer.filter(id=settings.default_download_server_id).first()
            if server:
                default_download_server = {
                    "id": server.id,
                    "name": server.name,
                    "server_type": server.server_type,
                    "base_url": server.base_url,
                    "description": server.description,
                }

        # 获取默认媒体服务器信息
        default_media_server = None
        if settings.default_media_server_id:
            server = await MediaServer.filter(id=settings.default_media_server_id).first()
            if server:
                default_media_server = {
                    "id": server.id,
                    "name": server.name,
                    "server_type": server.server_type,
                    "base_url": server.base_url,
                    "description": server.description,
                }

        return {
            "id": settings.id,
            "default_download_server_id": settings.default_download_server_id,
            "default_download_server": default_download_server,
            "default_media_server_id": settings.default_media_server_id,
            "default_media_server": default_media_server,
            "enable_path_replacement": settings.enable_path_replacement,
            "replacement_path": settings.replacement_path,
            "download_threads": settings.download_threads,
            "output_directory": settings.output_directory,
            "video_file_types": settings.video_file_types,
            "audio_file_types": settings.audio_file_types,
            "image_file_types": settings.image_file_types,
            "subtitle_file_types": settings.subtitle_file_types,
            "metadata_file_types": settings.metadata_file_types,
            # 任务恢复配置
            "enable_task_recovery_periodic_check": settings.enable_task_recovery_periodic_check,
            "task_recovery_check_interval": settings.task_recovery_check_interval,
            "task_timeout_hours": settings.task_timeout_hours,
            "heartbeat_timeout_minutes": settings.heartbeat_timeout_minutes,
            "activity_check_minutes": settings.activity_check_minutes,
            "recent_activity_minutes": settings.recent_activity_minutes,
            # 重试配置
            "failure_retry_count": settings.failure_retry_count,
            "retry_interval_seconds": settings.retry_interval_seconds,
            # 日志配置
            "enable_sql_logging": settings.enable_sql_logging,
            "update_time": settings.update_time,
            "settings_version": settings.settings_version,
        }

    def _validate_file_extensions(self, data: Dict[str, Any]) -> None:
        """
        验证文件扩展名是否有重复

        检查所有文件类型中的扩展名，确保每个扩展名只在一种文件类型中出现
        以及确保同一类型内没有重复的扩展名

        Args:
            data: 包含文件类型字段的系统设置数据

        Raises:
            HTTPException: 当发现不同文件类型中有重复扩展名或同一类型内有重复扩展名时抛出异常
        """
        # 定义文件类型字段映射关系
        file_type_fields = {
            FileType.VIDEO: "video_file_types",
            FileType.AUDIO: "audio_file_types",
            FileType.IMAGE: "image_file_types",
            FileType.SUBTITLE: "subtitle_file_types",
            FileType.METADATA: "metadata_file_types",
        }

        # 映射用于显示中文名称
        file_type_names = {
            FileType.VIDEO: "视频",
            FileType.AUDIO: "音频",
            FileType.IMAGE: "图片",
            FileType.SUBTITLE: "字幕",
            FileType.METADATA: "元数据",
        }

        # 记录每个类型内部的扩展名，用于检查同一类型内的重复
        type_extensions: Dict[str, Set[str]] = {}
        # 记录同一类型内的重复扩展名
        internal_duplicates: List[Tuple[str, str]] = []

        # 存储所有扩展名及其所属类型，用于检查跨类型的重复
        extension_to_type: Dict[str, str] = {}
        # 记录跨类型重复的扩展名
        cross_type_duplicates: List[Tuple[str, str, str]] = []

        # 遍历所有文件类型字段
        for file_type, field_name in file_type_fields.items():
            if field_name not in data or not data[field_name]:
                continue

            # 为该类型初始化扩展名集合
            if file_type not in type_extensions:
                type_extensions[file_type] = set()

            # 解析扩展名，使用逗号分隔
            extensions = [ext.strip() for ext in data[field_name].split(",") if ext.strip()]

            for ext in extensions:
                # 确保扩展名格式正确（以点开头）
                if not ext.startswith("."):
                    ext = f".{ext}"

                # 转为小写用于比较
                ext_lower = ext.lower()

                # 检查同一类型内是否有重复
                if ext_lower in type_extensions[file_type]:
                    internal_duplicates.append((ext, file_type_names[file_type]))
                else:
                    type_extensions[file_type].add(ext_lower)

                # 检查是否在不同类型间有重复
                if ext_lower in extension_to_type:
                    existing_type = extension_to_type[ext_lower]
                    if existing_type != file_type:  # 只有不同类型间的重复才记录
                        cross_type_duplicates.append((ext, file_type_names[existing_type], file_type_names[file_type]))
                else:
                    extension_to_type[ext_lower] = file_type

        # 处理错误情况
        error_messages = []

        # 处理同一类型内的重复
        if internal_duplicates:
            internal_duplicate_details = []
            for ext, type_name in internal_duplicates:
                internal_duplicate_details.append(f"扩展名 {ext} 在 {type_name} 类型中重复")

            error_messages.append(
                "文件类型设置有误：同一类型内不能有重复的文件后缀。\n" + "\n".join(internal_duplicate_details)
            )

        # 处理跨类型的重复
        if cross_type_duplicates:
            cross_duplicate_details = []
            for ext, type1, type2 in cross_type_duplicates:
                cross_duplicate_details.append(f"扩展名 {ext} 在 {type1} 和 {type2} 类型中均存在")

            error_messages.append(
                "文件类型设置有误：同一个文件后缀不能属于不同的文件类型。\n" + "\n".join(cross_duplicate_details)
            )

        # 如果有任何错误，抛出异常
        if error_messages:
            raise HTTPException(code=400, msg="\n\n".join(error_messages))

    async def create_or_update_settings(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建或更新系统设置

        如果不存在设置记录，创建新记录；如果存在，则更新现有记录

        Args:
            data: 系统设置数据

        Returns:
            更新后的系统设置字典
        """
        # 获取当前用户ID
        user_id = CTX_USER_ID.get()

        # 移除可能存在的default_server_id字段
        if "default_server_id" in data:
            data.pop("default_server_id")

        # 检查各种服务器ID是否存在
        server_id_fields = ["default_download_server_id", "default_media_server_id"]

        for field in server_id_fields:
            if data.get(field):
                server = await MediaServer.filter(id=data[field]).first()
                if not server:
                    raise HTTPException(code=404, msg=f"指定的服务器ID不存在: {data[field]}")

        # 验证文件类型扩展名是否有重复
        self._validate_file_extensions(data)

        # 查找现有设置
        settings = await SystemSettings.all().first()

        # 检查是否需要更新版本号（当文件类型相关设置发生变化时）
        file_type_fields = [
            "video_file_types",
            "audio_file_types",
            "image_file_types",
            "subtitle_file_types",
            "metadata_file_types",
        ]

        need_version_update = False

        if settings:
            # 检查文件类型相关设置是否有变化
            for field in file_type_fields:
                if field in data and getattr(settings, field) != data[field]:
                    need_version_update = True
                    break

            # 更新现有设置
            for field, value in data.items():
                # 确认模型中有该字段才进行设置
                if hasattr(settings, field):
                    setattr(settings, field, value)

            # 如果需要更新版本号，则递增版本号
            if need_version_update:
                settings.settings_version = settings.settings_version + 1
                # print(f"文件类型设置已变更，增加设置版本号至: {settings.settings_version}")

            settings.updated_by_id = user_id
            await settings.save()
        else:
            # 确保只包含模型中存在的字段进行创建
            valid_data = {}
            model_instance = SystemSettings()
            for field, value in data.items():
                if hasattr(model_instance, field):
                    valid_data[field] = value

            # 创建新设置，初始版本号为1
            settings = await SystemSettings.create(**valid_data, settings_version=1, updated_by_id=user_id)
            print(f"创建新的系统设置，初始设置版本号: 1")

        # 如果更新了SQL日志设置，立即应用
        if 'enable_sql_logging' in data:
            set_sql_logging_enabled(data['enable_sql_logging'])

        # 获取更新后的设置
        return await self.get_settings()


system_settings_controller = SystemSettingsController(SystemSettings)
